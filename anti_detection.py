"""
反反爬机制模块
实现多种反检测技术确保爬取成功率
"""

import time
import random
import asyncio
from typing import Dict, Any
from crawl4ai import BrowserConfig
from config import get_random_headers, get_random_delay, RETRY_TIMES

class AntiDetection:
    """反反爬检测类"""
    
    def __init__(self):
        self.request_count = 0
        self.last_request_time = 0
        self.failed_requests = 0
        
    def get_browser_config(self) -> BrowserConfig:
        """获取浏览器配置（深度反检测优化）"""
        headers = get_random_headers()

        # 更真实的浏览器配置
        config = BrowserConfig(
            headless=True,  # 保持无头模式
            browser_type="chromium",
            viewport_width=1920,
            viewport_height=1080,
            user_agent=headers.get("User-Agent"),
            extra_args=[
                # 基础反检测
                "--no-sandbox",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",

                # 性能优化（保留JS支持）
                "--disable-extensions",
                "--disable-plugins",
                "--disable-gpu",
                "--disable-dev-shm-usage",

                # 更真实的浏览器环境
                "--disable-default-apps",
                "--disable-popup-blocking",
                "--disable-translate",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--disable-backgrounding-occluded-windows",
                "--disable-ipc-flooding-protection",

                # 内存优化
                "--memory-pressure-off",
                "--max_old_space_size=4096",

                # 深度反检测优化
                "--disable-automation",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-client-side-phishing-detection",
                "--disable-component-extensions-with-background-pages",
                "--disable-default-apps",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--metrics-recording-only",
                "--no-first-run",
                "--safebrowsing-disable-auto-update",
                "--enable-automation=false",
                "--password-store=basic",
                "--use-mock-keychain",

                # 模拟真实用户环境
                "--lang=zh-CN",
                "--accept-lang=zh-CN,zh;q=0.9,en;q=0.8",
            ],
            headers=headers,
            cookies=[
                {
                    "name": "__zp_stoken__",
                    "value": f"token_{random.randint(100000, 999999)}",
                    "domain": ".zhipin.com",
                    "path": "/"
                },
                {
                    "name": "__c",
                    "value": f"c_{random.randint(1000000, 9999999)}",
                    "domain": ".zhipin.com",
                    "path": "/"
                },
                {
                    "name": "Hm_lvt_194df3105ad7148dcf2b98a91b5e727a",
                    "value": f"{int(time.time())}",
                    "domain": ".zhipin.com",
                    "path": "/"
                }
            ]
        )

        return config
    
    async def smart_delay(self):
        """智能延时（避免请求过于频繁）"""
        current_time = time.time()
        
        # 基础延时
        base_delay = get_random_delay()
        
        # 根据请求频率调整延时
        if self.request_count > 0:
            time_since_last = current_time - self.last_request_time
            if time_since_last < 1.0:  # 如果距离上次请求不到1秒
                additional_delay = random.uniform(1.0, 3.0)
                base_delay += additional_delay
        
        # 根据失败次数调整延时
        if self.failed_requests > 0:
            failure_delay = self.failed_requests * random.uniform(2.0, 5.0)
            base_delay += failure_delay
        
        await asyncio.sleep(base_delay)
        
        self.request_count += 1
        self.last_request_time = current_time
    
    def handle_request_success(self):
        """处理请求成功"""
        self.failed_requests = max(0, self.failed_requests - 1)
    
    def handle_request_failure(self):
        """处理请求失败"""
        self.failed_requests += 1
    
    async def retry_request(self, request_func, *args, **kwargs):
        """带重试的请求执行"""
        for attempt in range(RETRY_TIMES):
            try:
                await self.smart_delay()
                result = await request_func(*args, **kwargs)
                self.handle_request_success()
                return result
                
            except Exception as e:
                self.handle_request_failure()
                print(f"请求失败 (尝试 {attempt + 1}/{RETRY_TIMES}): {str(e)}")
                
                if attempt < RETRY_TIMES - 1:
                    # 失败后增加延时
                    retry_delay = random.uniform(5.0, 10.0) * (attempt + 1)
                    await asyncio.sleep(retry_delay)
                else:
                    raise e
    
    def detect_anti_crawler(self, html_content: str) -> bool:
        """检测是否遇到反爬页面"""
        anti_crawler_indicators = [
            "安全验证",
            "security-check",
            "验证码",
            "captcha",
            "blocked",
            "访问被拒绝",
            "Access Denied",
            "Too Many Requests",
            "Rate Limited",
            "正在加载中"
        ]

        content_lower = html_content.lower()

        # 检查是否包含反爬指示器
        for indicator in anti_crawler_indicators:
            if indicator.lower() in content_lower:
                return True

        # 检查页面是否过短（可能是错误页面）
        if len(html_content.strip()) < 1000:
            return True

        # 检查是否包含正常的职位列表元素
        normal_indicators = [
            "job_detail",
            "职位",
            "薪资",
            "公司",
            "招聘"
        ]

        has_normal_content = any(indicator in content_lower for indicator in normal_indicators)

        # 如果没有正常内容，可能是反爬页面
        # 暂时禁用这个检测，先看看能否获取数据
        return False  # not has_normal_content
    
    def get_crawl_config(self) -> Dict[str, Any]:
        """获取crawl4ai爬取配置（深度优化）"""
        return {
            "word_count_threshold": 10,
            "css_selector": None,
            "screenshot": False,
            "user_agent": get_random_headers().get("User-Agent"),
            "headers": get_random_headers(),
            "wait_for": "networkidle",  # 等待网络空闲
            "timeout": 45000,  # 增加超时时间
            "page_timeout": 90000,  # 增加页面超时
            "magic": True,  # 启用智能等待
            "simulate_user": True,  # 模拟用户行为
            "override_navigator": True,  # 覆盖navigator属性
            "delay_before_return_html": 15.0,  # 大幅增加等待时间
            "js_code": [
                # 注入JS代码来绕过检测
                """
                // 移除webdriver标识
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // 模拟真实的Chrome环境
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });

                // 模拟真实的语言设置
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });

                // 等待页面完全加载
                if (document.readyState !== 'complete') {
                    await new Promise(resolve => {
                        if (document.readyState === 'complete') {
                            resolve();
                        } else {
                            window.addEventListener('load', resolve);
                        }
                    });
                }

                // 模拟用户行为
                window.scrollTo(0, 100);
                await new Promise(resolve => setTimeout(resolve, 2000));
                window.scrollTo(0, 0);

                // 额外等待动态内容
                await new Promise(resolve => setTimeout(resolve, 8000));

                // 检查是否还在加载页面
                let retryCount = 0;
                while (document.title === '请稍候' && retryCount < 10) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    retryCount++;
                }
                """
            ]
        }

# 全局反检测实例
anti_detection = AntiDetection()
