"""
BOSS直聘超高性能全站爬虫
目标：5页数据5秒内完成，100%准确提取7个字段
"""

import asyncio
import time
from typing import List, Dict, Any
from crawl4ai import AsyncWebCrawler
from urllib.parse import urljoin

from config import BASE_URL, SEARCH_URL, MAX_CONCURRENT_REQUESTS, CITY_CODES
from anti_detection import anti_detection
from data_extractor import data_extractor
from data_processor import data_processor

class BOSSCrawler:
    """BOSS直聘高性能爬虫"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.search_url = SEARCH_URL
        self.crawler = None
        self.total_jobs = []
        self.semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def start(self):
        """启动爬虫"""
        browser_config = anti_detection.get_browser_config()
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.start()
        print("爬虫已启动")
    
    async def close(self):
        """关闭爬虫"""
        if self.crawler:
            await self.crawler.close()
        print("爬虫已关闭")
    
    async def crawl_job_list_page(self, url: str) -> List[str]:
        """爬取职位列表页面，提取所有职位URL"""
        try:
            crawl_config = anti_detection.get_crawl_config()
            
            result = await anti_detection.retry_request(
                self.crawler.arun,
                url=url,
                **crawl_config
            )
            
            if not result.success:
                print(f"列表页爬取失败: {url}")
                return []
            
            # 检测反爬
            if anti_detection.detect_anti_crawler(result.html):
                print(f"检测到反爬页面: {url}")
                return []
            
            # 提取职位URL
            job_urls = data_extractor.extract_job_urls_from_list(result.html)
            return job_urls
            
        except Exception as e:
            print(f"爬取列表页异常 {url}: {str(e)}")
            return []
    
    async def crawl_job_detail(self, job_url: str) -> Dict[str, str]:
        """爬取单个职位详情"""
        async with self.semaphore:  # 控制并发数
            try:
                crawl_config = anti_detection.get_crawl_config()
                
                result = await anti_detection.retry_request(
                    self.crawler.arun,
                    url=job_url,
                    **crawl_config
                )
                
                if not result.success:
                    print(f"详情页爬取失败: {job_url}")
                    return None
                
                # 检测反爬
                if anti_detection.detect_anti_crawler(result.html):
                    print(f"检测到反爬页面: {job_url}")
                    return None
                
                # 提取职位数据
                job_data = data_extractor.extract_job_details(result.html, job_url)
                
                if job_data and data_extractor.validate_job_data(job_data):
                    print(f"✓ 成功提取: {job_data.get('岗位名称', 'Unknown')}")
                    return job_data
                else:
                    print(f"✗ 数据不完整: {job_url}")
                    return None
                
            except Exception as e:
                print(f"爬取详情页异常 {job_url}: {str(e)}")
                return None
    
    async def crawl_multiple_pages(self, base_search_url: str, max_pages: int = 5) -> List[str]:
        """爬取多页职位列表"""
        all_job_urls = []
        
        for page in range(1, max_pages + 1):
            # 构建分页URL
            page_url = f"{base_search_url}?page={page}"
            print(f"正在爬取第 {page} 页: {page_url}")
            
            job_urls = await self.crawl_job_list_page(page_url)
            
            if not job_urls:
                print(f"第 {page} 页没有获取到职位URL，可能已到最后一页")
                break
            
            all_job_urls.extend(job_urls)
            print(f"第 {page} 页获取到 {len(job_urls)} 个职位URL")
        
        # 去重
        unique_urls = list(set(all_job_urls))
        print(f"总共获取到 {len(unique_urls)} 个唯一职位URL")
        
        return unique_urls
    
    async def crawl_jobs_batch(self, job_urls: List[str]) -> List[Dict[str, str]]:
        """批量爬取职位详情（高性能并发）"""
        print(f"开始并发爬取 {len(job_urls)} 个职位详情...")
        start_time = time.time()
        
        # 创建并发任务
        tasks = [self.crawl_job_detail(url) for url in job_urls]
        
        # 执行并发爬取
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤有效结果
        valid_jobs = []
        for result in results:
            if isinstance(result, dict) and result:
                valid_jobs.append(result)
            elif isinstance(result, Exception):
                print(f"任务异常: {str(result)}")
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"并发爬取完成！")
        print(f"耗时: {elapsed_time:.2f} 秒")
        print(f"成功率: {len(valid_jobs)}/{len(job_urls)} ({len(valid_jobs)/len(job_urls)*100:.1f}%)")
        print(f"平均速度: {len(valid_jobs)/elapsed_time:.2f} 个/秒")
        
        return valid_jobs
    
    async def crawl_city_jobs(self, city_name: str = None, max_pages: int = 5) -> List[Dict[str, str]]:
        """爬取指定城市的职位（默认不限制城市）"""
        if city_name and city_name in CITY_CODES:
            city_code = CITY_CODES[city_name]
            search_url = f"{BASE_URL}/c{city_code}/"
            print(f"爬取城市: {city_name} (代码: {city_code})")
        else:
            search_url = SEARCH_URL
            print("爬取全国职位")
        
        # 1. 获取职位URL列表
        job_urls = await self.crawl_multiple_pages(search_url, max_pages)
        
        if not job_urls:
            print("没有获取到职位URL")
            return []
        
        # 2. 并发爬取职位详情
        jobs_data = await self.crawl_jobs_batch(job_urls)
        
        return jobs_data
    
    async def run_full_crawl(self, city_name: str = None, max_pages: int = 5) -> Dict[str, str]:
        """执行完整的爬取流程"""
        print("=" * 60)
        print("BOSS直聘超高性能爬虫启动")
        print(f"目标: {max_pages} 页数据，预期 5 秒内完成")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            # 爬取职位数据
            jobs_data = await self.crawl_city_jobs(city_name, max_pages)
            
            if not jobs_data:
                print("没有爬取到有效数据")
                return {"json": "", "excel": ""}
            
            # 处理和保存数据
            file_paths = data_processor.process_and_save(jobs_data)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            print("=" * 60)
            print("爬取任务完成！")
            print(f"总耗时: {total_time:.2f} 秒")
            print(f"爬取数量: {len(jobs_data)} 条")
            print(f"平均速度: {len(jobs_data)/total_time:.2f} 条/秒")
            print(f"性能目标: {'✓ 达成' if total_time <= 5 else '✗ 未达成'} (目标: 5秒)")
            print(f"JSON文件: {file_paths['json']}")
            print(f"Excel文件: {file_paths['excel']}")
            print("=" * 60)
            
            return file_paths
            
        except Exception as e:
            print(f"爬取过程发生异常: {str(e)}")
            return {"json": "", "excel": ""}

async def main():
    """主函数"""
    # 使用异步上下文管理器
    async with BOSSCrawler() as crawler:
        # 执行爬取（可以修改参数）
        result = await crawler.run_full_crawl(
            city_name=None,  # None表示全国，可指定城市如"北京"
            max_pages=5      # 爬取页数
        )
        
        return result

if __name__ == "__main__":
    # 运行爬虫
    asyncio.run(main())
