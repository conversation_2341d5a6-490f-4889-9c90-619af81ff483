"""
BOSS直聘爬虫配置文件
高性能反反爬配置
"""

import random

# 基础配置
BASE_URL = "https://www.zhipin.com"
SEARCH_URL = "https://www.zhipin.com/web/geek/jobs"

# 性能配置
MAX_CONCURRENT_REQUESTS = 20  # 最大并发请求数
REQUEST_DELAY_RANGE = (0.5, 2.0)  # 请求延时范围（秒）
RETRY_TIMES = 3  # 重试次数
TIMEOUT = 30  # 请求超时时间

# User-Agent池（轮换使用）
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0",
]

# 请求头模板
HEADERS_TEMPLATES = [
    {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1",
    },
    {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Upgrade-Insecure-Requests": "1",
    }
]

# CSS选择器配置（用于数据提取）
SELECTORS = {
    # 职位列表页选择器
    "job_list": "li[data-jobid]",
    "job_link": "a[href*='/job_detail/']",
    "next_page": "a[ka='page-next']",
    
    # 职位详情页选择器
    "job_title": "h1.job-title",
    "salary": ".job-primary .salary",
    "benefits": ".job-tags .tag-list span",
    "job_description": ".job-detail-section .text",
    "company_intro": ".company-info .company-text",
    "work_location": ".location-address",
    "company_name": ".company-info .company-name a",
}

# 输出配置
OUTPUT_DIR = "output"
JSON_FILENAME = "boss_jobs.json"
EXCEL_FILENAME = "boss_jobs.xlsx"

# 城市代码映射（主要城市）
CITY_CODES = {
    "北京": "*********",
    "上海": "*********", 
    "广州": "*********",
    "深圳": "*********",
    "杭州": "*********",
    "南京": "*********",
    "武汉": "*********",
    "成都": "*********",
    "西安": "*********",
    "重庆": "*********",
}

# 职位分类代码（热门职位）
POSITION_CODES = {
    "技术": "100000",
    "产品": "110000", 
    "设计": "120000",
    "运营": "130000",
    "市场": "140000",
    "销售": "140000",
    "职能": "150000",
    "金融": "180000",
    "教育": "190000",
}

def get_random_user_agent():
    """获取随机User-Agent"""
    return random.choice(USER_AGENTS)

def get_random_headers():
    """获取随机请求头"""
    headers = random.choice(HEADERS_TEMPLATES).copy()
    headers["User-Agent"] = get_random_user_agent()
    return headers

def get_random_delay():
    """获取随机延时"""
    return random.uniform(*REQUEST_DELAY_RANGE)
