"""
数据提取器模块
精确提取BOSS直聘职位的7个必需字段
"""

import re
from typing import Dict, List, Optional
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from config import BASE_URL, SELECTORS

class DataExtractor:
    """数据提取器类"""
    
    def __init__(self):
        self.base_url = BASE_URL
    
    def extract_job_urls_from_list(self, html_content: str) -> List[str]:
        """从职位列表页面提取所有职位详情URL"""
        soup = BeautifulSoup(html_content, 'html.parser')
        job_urls = []
        
        try:
            # 查找所有职位链接
            job_links = soup.select("a[href*='/job_detail/']")
            
            for link in job_links:
                href = link.get('href')
                if href and '/job_detail/' in href:
                    # 构建完整URL
                    full_url = urljoin(self.base_url, href)
                    if full_url not in job_urls:
                        job_urls.append(full_url)
            
            print(f"从列表页提取到 {len(job_urls)} 个职位URL")
            
        except Exception as e:
            print(f"提取职位URL失败: {str(e)}")
        
        return job_urls
    
    def extract_job_details(self, html_content: str, job_url: str) -> Optional[Dict[str, str]]:
        """从职位详情页面提取完整的职位信息"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        try:
            # 1. 岗位名称
            job_title = self._extract_job_title(soup)
            
            # 2. 薪资情况  
            salary = self._extract_salary(soup)
            
            # 3. 待遇情况
            benefits = self._extract_benefits(soup)
            
            # 4. 职位描述
            job_description = self._extract_job_description(soup)
            
            # 5. 公司简介
            company_intro = self._extract_company_intro(soup)
            
            # 6. 工作地点
            work_location = self._extract_work_location(soup)
            
            # 7. 实际网址
            actual_url = job_url
            
            # 验证数据完整性
            if not job_title or not salary:
                print(f"关键数据缺失，跳过该职位: {job_url}")
                return None
            
            job_data = {
                "岗位名称": job_title,
                "薪资情况": salary,
                "待遇情况": benefits,
                "职位描述": job_description,
                "公司简介": company_intro,
                "工作地点": work_location,
                "实际网址": actual_url
            }
            
            return job_data
            
        except Exception as e:
            print(f"提取职位详情失败 {job_url}: {str(e)}")
            return None
    
    def _extract_job_title(self, soup: BeautifulSoup) -> str:
        """提取岗位名称"""
        selectors = [
            "h1.job-title",
            ".job-primary h1",
            ".job-name",
            "h1[class*='job']",
            ".job-detail h1"
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        
        return ""
    
    def _extract_salary(self, soup: BeautifulSoup) -> str:
        """提取薪资情况"""
        selectors = [
            ".job-primary .salary",
            ".salary",
            "[class*='salary']",
            ".job-limit .red"
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                salary_text = element.get_text(strip=True)
                # 清理薪资文本
                salary_text = re.sub(r'\s+', '', salary_text)
                if any(char in salary_text for char in ['K', '万', '元', '-']):
                    return salary_text
        
        return ""
    
    def _extract_benefits(self, soup: BeautifulSoup) -> str:
        """提取待遇情况（福利）"""
        selectors = [
            ".job-tags .tag-list span",
            ".welfare-list span",
            ".job-welfare span",
            "[class*='welfare'] span",
            "[class*='benefit'] span"
        ]
        
        benefits = []
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                benefit_text = element.get_text(strip=True)
                if benefit_text and benefit_text not in benefits:
                    benefits.append(benefit_text)
        
        return "，".join(benefits) if benefits else ""
    
    def _extract_job_description(self, soup: BeautifulSoup) -> str:
        """提取职位描述"""
        selectors = [
            ".job-detail-section .text",
            ".job-sec .text",
            ".job-detail .text",
            ".detail-content",
            "[class*='job-detail']",
            ".job-description"
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                # 获取文本并清理
                description = element.get_text(separator='\n', strip=True)
                # 清理多余的空白字符
                description = re.sub(r'\n\s*\n', '\n', description)
                description = re.sub(r'\s+', ' ', description)
                if len(description) > 50:  # 确保描述有足够内容
                    return description
        
        return ""
    
    def _extract_company_intro(self, soup: BeautifulSoup) -> str:
        """提取公司简介"""
        selectors = [
            ".company-info .company-text",
            ".company-detail .text",
            ".company-intro",
            "[class*='company-intro']",
            ".company-description"
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                intro = element.get_text(strip=True)
                if len(intro) > 20:  # 确保简介有足够内容
                    return intro
        
        return ""
    
    def _extract_work_location(self, soup: BeautifulSoup) -> str:
        """提取工作地点"""
        selectors = [
            ".location-address",
            ".work-addr",
            ".job-location",
            "[class*='location']",
            "[class*='address']"
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                location = element.get_text(strip=True)
                if location:
                    return location
        
        # 尝试从其他位置提取地址信息
        location_elements = soup.select(".job-primary .job-area, .job-limit .job-area")
        for element in location_elements:
            location = element.get_text(strip=True)
            if location:
                return location
        
        return ""
    
    def validate_job_data(self, job_data: Dict[str, str]) -> bool:
        """验证职位数据的完整性"""
        required_fields = ["岗位名称", "薪资情况"]
        
        for field in required_fields:
            if not job_data.get(field):
                return False
        
        return True

# 全局数据提取器实例
data_extractor = DataExtractor()
