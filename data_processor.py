"""
数据处理和输出模块
处理爬取的数据并输出为JSON和Excel格式
"""

import json
import os
import pandas as pd
from typing import List, Dict, Any
from datetime import datetime
from config import OUTPUT_DIR, JSON_FILENAME, EXCEL_FILENAME

class DataProcessor:
    """数据处理器类"""
    
    def __init__(self):
        self.output_dir = OUTPUT_DIR
        self.ensure_output_dir()
        self.processed_urls = set()  # 用于去重
    
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def deduplicate_jobs(self, jobs_data: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """数据去重"""
        unique_jobs = []
        seen_urls = set()
        seen_titles_companies = set()
        
        for job in jobs_data:
            url = job.get("实际网址", "")
            title = job.get("岗位名称", "")
            
            # 基于URL去重
            if url in seen_urls:
                continue
            
            # 基于岗位名称+公司的组合去重（从公司简介中提取公司名）
            company_name = self._extract_company_name(job.get("公司简介", ""))
            title_company_key = f"{title}_{company_name}"
            
            if title_company_key in seen_titles_companies:
                continue
            
            seen_urls.add(url)
            seen_titles_companies.add(title_company_key)
            unique_jobs.append(job)
        
        print(f"去重前: {len(jobs_data)} 条数据，去重后: {len(unique_jobs)} 条数据")
        return unique_jobs
    
    def _extract_company_name(self, company_intro: str) -> str:
        """从公司简介中提取公司名称"""
        if not company_intro:
            return ""
        
        # 简单提取：取前20个字符作为公司标识
        return company_intro[:20].strip()
    
    def validate_and_clean_data(self, jobs_data: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """验证和清理数据"""
        cleaned_jobs = []
        
        for job in jobs_data:
            # 验证必需字段
            if not job.get("岗位名称") or not job.get("薪资情况"):
                continue
            
            # 清理数据
            cleaned_job = {}
            for key, value in job.items():
                if isinstance(value, str):
                    # 清理字符串数据
                    cleaned_value = value.strip()
                    cleaned_value = cleaned_value.replace('\n', ' ').replace('\r', ' ')
                    cleaned_value = ' '.join(cleaned_value.split())  # 合并多个空格
                    cleaned_job[key] = cleaned_value
                else:
                    cleaned_job[key] = value
            
            # 添加爬取时间
            cleaned_job["爬取时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            cleaned_jobs.append(cleaned_job)
        
        return cleaned_jobs
    
    def save_to_json(self, jobs_data: List[Dict[str, str]], filename: str = None) -> str:
        """保存数据为JSON格式"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"boss_jobs_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(jobs_data, f, ensure_ascii=False, indent=2)
            
            print(f"JSON数据已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"保存JSON文件失败: {str(e)}")
            return ""
    
    def save_to_excel(self, jobs_data: List[Dict[str, str]], filename: str = None) -> str:
        """保存数据为Excel格式"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"boss_jobs_{timestamp}.xlsx"
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            # 创建DataFrame
            df = pd.DataFrame(jobs_data)
            
            # 调整列顺序
            column_order = [
                "岗位名称", "薪资情况", "待遇情况", "职位描述", 
                "公司简介", "工作地点", "实际网址", "爬取时间"
            ]
            
            # 确保所有列都存在
            for col in column_order:
                if col not in df.columns:
                    df[col] = ""
            
            df = df[column_order]
            
            # 保存到Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='BOSS直聘职位数据', index=False)
                
                # 调整列宽
                worksheet = writer.sheets['BOSS直聘职位数据']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            print(f"Excel数据已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"保存Excel文件失败: {str(e)}")
            return ""
    
    def process_and_save(self, jobs_data: List[Dict[str, str]]) -> Dict[str, str]:
        """处理数据并保存为两种格式"""
        if not jobs_data:
            print("没有数据需要处理")
            return {"json": "", "excel": ""}
        
        print(f"开始处理 {len(jobs_data)} 条职位数据...")
        
        # 数据去重
        unique_jobs = self.deduplicate_jobs(jobs_data)
        
        # 数据验证和清理
        cleaned_jobs = self.validate_and_clean_data(unique_jobs)
        
        if not cleaned_jobs:
            print("清理后没有有效数据")
            return {"json": "", "excel": ""}
        
        print(f"最终有效数据: {len(cleaned_jobs)} 条")
        
        # 保存为JSON和Excel
        json_path = self.save_to_json(cleaned_jobs)
        excel_path = self.save_to_excel(cleaned_jobs)
        
        # 打印统计信息
        self.print_statistics(cleaned_jobs)
        
        return {"json": json_path, "excel": excel_path}
    
    def print_statistics(self, jobs_data: List[Dict[str, str]]):
        """打印数据统计信息"""
        if not jobs_data:
            return
        
        print("\n=== 数据统计 ===")
        print(f"总职位数量: {len(jobs_data)}")
        
        # 统计薪资分布
        salary_ranges = {}
        for job in jobs_data:
            salary = job.get("薪资情况", "")
            if "K" in salary:
                salary_ranges[salary] = salary_ranges.get(salary, 0) + 1
        
        print(f"薪资分布 (前10): {dict(list(salary_ranges.items())[:10])}")
        
        # 统计有福利的职位
        benefits_count = sum(1 for job in jobs_data if job.get("待遇情况"))
        print(f"有福利信息的职位: {benefits_count} 条 ({benefits_count/len(jobs_data)*100:.1f}%)")
        
        # 统计有公司简介的职位
        intro_count = sum(1 for job in jobs_data if job.get("公司简介"))
        print(f"有公司简介的职位: {intro_count} 条 ({intro_count/len(jobs_data)*100:.1f}%)")
        
        print("=== 统计完成 ===\n")

# 全局数据处理器实例
data_processor = DataProcessor()
