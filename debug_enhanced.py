"""
增强版爬虫调试脚本
分析页面内容和数据提取问题
"""

import asyncio
from crawl4ai import AsyncWebCrawler
from anti_detection import anti_detection
from data_extractor import data_extractor

async def debug_single_job():
    """调试单个职位页面"""
    print("🔍 开始调试单个职位页面...")

    browser_config = anti_detection.get_browser_config()
    crawler = AsyncWebCrawler(config=browser_config)

    try:
        await crawler.start()

        test_url = "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html"

        print(f"正在访问: {test_url}")

        # 获取页面内容
        crawl_config = anti_detection.get_crawl_config()
        result = await crawler.arun(url=test_url, **crawl_config)
        
        if result.success:
            print(f"✅ 页面获取成功")
            print(f"📄 页面长度: {len(result.html)}")
            print(f"📋 页面标题: {result.html[:200]}...")
            
            # 保存页面内容用于分析
            with open("debug_page.html", "w", encoding="utf-8") as f:
                f.write(result.html)
            print("📁 页面内容已保存到 debug_page.html")
            
            # 尝试数据提取
            print("\n🎯 开始数据提取测试...")
            job_data = data_extractor.extract_job_details(result.html, test_url)
            
            if job_data:
                print("✅ 数据提取成功!")
                for key, value in job_data.items():
                    print(f"   {key}: {value[:100]}..." if len(str(value)) > 100 else f"   {key}: {value}")
            else:
                print("❌ 数据提取失败")
                
                # 分析页面内容
                print("\n🔍 页面内容分析:")
                
                # 检查是否包含关键词
                keywords = ["职位", "薪资", "工作", "招聘", "公司", "岗位"]
                for keyword in keywords:
                    count = result.html.count(keyword)
                    print(f"   '{keyword}': {count} 次")
                
                # 检查页面结构
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(result.html, 'html.parser')
                
                print(f"\n📊 页面结构分析:")
                print(f"   总元素数: {len(soup.find_all())}")
                print(f"   h1标签数: {len(soup.find_all('h1'))}")
                print(f"   div标签数: {len(soup.find_all('div'))}")
                print(f"   span标签数: {len(soup.find_all('span'))}")
                
                # 查找可能的职位信息
                print(f"\n🎯 可能的职位信息:")
                h1_tags = soup.find_all('h1')
                for i, h1 in enumerate(h1_tags[:5]):
                    print(f"   H1-{i+1}: {h1.get_text(strip=True)[:50]}...")
                
                # 查找可能的薪资信息
                salary_keywords = ['K', '万', '薪', '元']
                for keyword in salary_keywords:
                    elements = soup.find_all(text=lambda text: text and keyword in text)
                    for element in elements[:3]:
                        if len(element.strip()) < 50:
                            print(f"   可能薪资: {element.strip()}")
        else:
            print(f"❌ 页面获取失败: {result.error_message}")

    finally:
        await crawler.close()

async def debug_data_extraction():
    """调试数据提取逻辑"""
    print("\n🧪 测试数据提取逻辑...")
    
    # 创建测试HTML
    test_html = """
    <html>
    <head><title>测试职位</title></head>
    <body>
        <h1 class="job-title">Python开发工程师</h1>
        <div class="salary">15-25K</div>
        <div class="job-tags">
            <span>五险一金</span>
            <span>年终奖</span>
        </div>
        <div class="job-detail-section">
            <div class="text">负责后端开发工作...</div>
        </div>
        <div class="company-info">
            <div class="company-text">某科技公司简介...</div>
        </div>
        <div class="location-address">北京市海淀区</div>
    </body>
    </html>
    """
    
    job_data = data_extractor.extract_job_details(test_html, "test_url")
    
    if job_data:
        print("✅ 测试HTML数据提取成功!")
        for key, value in job_data.items():
            print(f"   {key}: {value}")
    else:
        print("❌ 测试HTML数据提取失败")

async def main():
    """主函数"""
    print("🚀 启动增强版爬虫调试程序")
    print("=" * 50)
    
    # 调试单个职位
    await debug_single_job()
    
    # 调试数据提取逻辑
    await debug_data_extraction()
    
    print("\n" + "=" * 50)
    print("🎉 调试完成!")

if __name__ == "__main__":
    asyncio.run(main())
