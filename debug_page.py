"""
调试页面内容脚本
"""

import asyncio
from crawl4ai import AsyncWebCrawler
from anti_detection import anti_detection

async def debug_page():
    """调试页面获取"""
    browser_config = anti_detection.get_browser_config()
    crawler = AsyncWebCrawler(config=browser_config)
    
    try:
        await crawler.start()
        
        crawl_config = anti_detection.get_crawl_config()
        url = "https://www.zhipin.com/web/geek/jobs"
        
        print(f"正在访问: {url}")
        result = await crawler.arun(url=url, **crawl_config)
        
        if result.success:
            print(f"页面获取成功!")
            print(f"页面长度: {len(result.html)}")
            print(f"页面标题: {result.html[:200]}")
            print("\n" + "="*50)
            print("页面内容前1000字符:")
            print(result.html[:1000])
            print("="*50)
            
            # 检查是否包含职位相关内容
            if "job_detail" in result.html.lower():
                print("✓ 包含job_detail")
            if "职位" in result.html:
                print("✓ 包含'职位'")
            if "招聘" in result.html:
                print("✓ 包含'招聘'")
            if "薪资" in result.html:
                print("✓ 包含'薪资'")
                
        else:
            print(f"页面获取失败: {result.error_message}")
            
    except Exception as e:
        print(f"异常: {str(e)}")
    finally:
        await crawler.close()

if __name__ == "__main__":
    asyncio.run(debug_page())
