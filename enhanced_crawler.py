"""
增强版BOSS直聘爬虫
深度优化反反爬和数据提取准确性
"""

import asyncio
import time
import random
from typing import List, Dict, Any
from crawl4ai import AsyncWebCrawler
from urllib.parse import urljoin

from config import BASE_URL, SEARCH_URL, MAX_CONCURRENT_REQUESTS, CITY_CODES
from anti_detection import anti_detection
from data_extractor import data_extractor
from data_processor import data_processor

class EnhancedBOSSCrawler:
    """增强版BOSS直聘爬虫"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.search_url = SEARCH_URL
        self.crawler = None
        self.total_jobs = []
        self.semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
        
        # 已知的职位URL列表（用于测试和绕过列表页限制）
        self.known_job_urls = [
            "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html",
            # 可以添加更多已知的职位URL
        ]
        
        # 不同的访问策略
        self.access_strategies = [
            self._strategy_direct_job_urls,
            self._strategy_search_api,
            self._strategy_mobile_interface,
            self._strategy_alternative_domains
        ]
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def start(self):
        """启动爬虫"""
        browser_config = anti_detection.get_browser_config()
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.start()
        print("增强版爬虫已启动")
    
    async def close(self):
        """关闭爬虫"""
        if self.crawler:
            await self.crawler.close()
        print("增强版爬虫已关闭")
    
    async def _strategy_direct_job_urls(self) -> List[str]:
        """策略1: 直接使用已知职位URL"""
        print("🎯 策略1: 使用已知职位URL")
        
        # 可以通过其他方式获取更多职位URL
        additional_urls = await self._discover_job_urls()
        
        all_urls = self.known_job_urls + additional_urls
        print(f"获取到 {len(all_urls)} 个职位URL")
        return all_urls
    
    async def _discover_job_urls(self) -> List[str]:
        """发现更多职位URL的方法"""
        discovered_urls = []
        
        # 方法1: 通过搜索引擎
        search_patterns = [
            "site:zhipin.com/job_detail/ 招聘",
            "site:zhipin.com/job_detail/ 工程师",
            "site:zhipin.com/job_detail/ 开发",
        ]
        
        # 方法2: 生成可能的URL模式
        # BOSS直聘的URL格式通常是固定的
        url_patterns = [
            "https://www.zhipin.com/job_detail/{}.html",
        ]
        
        # 生成一些可能的ID
        possible_ids = self._generate_possible_job_ids()
        
        for pattern in url_patterns:
            for job_id in possible_ids[:10]:  # 限制数量
                url = pattern.format(job_id)
                discovered_urls.append(url)
        
        return discovered_urls
    
    def _generate_possible_job_ids(self) -> List[str]:
        """生成可能的职位ID"""
        # 基于已知URL的模式生成
        base_patterns = [
            "f9d8c7acea814efa1XB93NS5FlRQ",  # 已知的ID格式
        ]
        
        generated_ids = []
        
        # 生成类似格式的ID
        for i in range(20):
            # 简单的ID生成逻辑
            random_id = f"{''.join(random.choices('abcdef0123456789', k=16))}1X{''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=8))}"
            generated_ids.append(random_id)
        
        return generated_ids
    
    async def _strategy_search_api(self) -> List[str]:
        """策略2: 尝试搜索API接口"""
        print("🔍 策略2: 尝试搜索API接口")
        
        api_urls = [
            "https://www.zhipin.com/wapi/zpgeek/search/joblist.json",
            "https://www.zhipin.com/wapi/zpgeek/search/job.json",
            "https://m.zhipin.com/api/job/search",
        ]
        
        job_urls = []
        
        for api_url in api_urls:
            try:
                # 构建API请求参数
                params = {
                    "scene": 1,
                    "query": "",
                    "city": 101010100,  # 北京
                    "experience": "",
                    "payType": "",
                    "partTime": "",
                    "degree": "",
                    "industry": "",
                    "scale": "",
                    "stage": "",
                    "position": "",
                    "jobType": "",
                    "salary": "",
                    "multiBusinessDistrict": "",
                    "multiSubway": "",
                    "page": 1,
                    "pageSize": 30
                }
                
                # 尝试访问API
                result = await self._api_request(api_url, params)
                if result:
                    urls = self._extract_urls_from_api_response(result)
                    job_urls.extend(urls)
                    
            except Exception as e:
                print(f"API请求失败 {api_url}: {str(e)}")
        
        print(f"API策略获取到 {len(job_urls)} 个职位URL")
        return job_urls
    
    async def _api_request(self, url: str, params: Dict) -> Dict:
        """发送API请求"""
        try:
            crawl_config = anti_detection.get_crawl_config()
            crawl_config["headers"]["Accept"] = "application/json"
            
            # 构建完整URL
            param_str = "&".join([f"{k}={v}" for k, v in params.items()])
            full_url = f"{url}?{param_str}"
            
            result = await self.crawler.arun(url=full_url, **crawl_config)
            
            if result.success:
                import json
                return json.loads(result.html)
            
        except Exception as e:
            print(f"API请求异常: {str(e)}")
        
        return {}
    
    def _extract_urls_from_api_response(self, response: Dict) -> List[str]:
        """从API响应中提取职位URL"""
        urls = []
        
        try:
            # 根据API响应格式提取
            if "zpData" in response:
                job_list = response["zpData"].get("jobList", [])
                for job in job_list:
                    if "encryptJobId" in job:
                        url = f"https://www.zhipin.com/job_detail/{job['encryptJobId']}.html"
                        urls.append(url)
            
            # 其他可能的响应格式
            if "data" in response:
                jobs = response["data"].get("jobs", [])
                for job in jobs:
                    if "id" in job:
                        url = f"https://www.zhipin.com/job_detail/{job['id']}.html"
                        urls.append(url)
                        
        except Exception as e:
            print(f"解析API响应失败: {str(e)}")
        
        return urls
    
    async def _strategy_mobile_interface(self) -> List[str]:
        """策略3: 尝试移动端接口"""
        print("📱 策略3: 尝试移动端接口")
        
        mobile_urls = [
            "https://m.zhipin.com/jobs",
            "https://m.zhipin.com/search/jobs",
        ]
        
        job_urls = []
        
        for mobile_url in mobile_urls:
            try:
                # 修改User-Agent为移动端
                mobile_config = anti_detection.get_crawl_config()
                mobile_config["user_agent"] = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
                mobile_config["headers"]["User-Agent"] = mobile_config["user_agent"]
                
                result = await self.crawler.arun(url=mobile_url, **mobile_config)
                
                if result.success:
                    urls = data_extractor.extract_job_urls_from_list(result.html)
                    job_urls.extend(urls)
                    
            except Exception as e:
                print(f"移动端访问失败 {mobile_url}: {str(e)}")
        
        print(f"移动端策略获取到 {len(job_urls)} 个职位URL")
        return job_urls
    
    async def _strategy_alternative_domains(self) -> List[str]:
        """策略4: 尝试其他域名或镜像"""
        print("🌐 策略4: 尝试其他域名")
        
        # 这里可以添加其他可能的域名或镜像站点
        alternative_domains = [
            # 暂时没有已知的镜像域名
        ]
        
        job_urls = []
        
        # 如果有其他域名，可以在这里实现
        print(f"其他域名策略获取到 {len(job_urls)} 个职位URL")
        return job_urls
    
    async def enhanced_crawl_job_detail(self, job_url: str) -> Dict[str, str]:
        """增强版职位详情爬取"""
        async with self.semaphore:
            try:
                crawl_config = anti_detection.get_crawl_config()
                
                result = await anti_detection.retry_request(
                    self.crawler.arun,
                    url=job_url,
                    **crawl_config
                )
                
                if not result.success:
                    print(f"详情页爬取失败: {job_url}")
                    return None
                
                # 更宽松的反爬检测
                if len(result.html) < 500:  # 页面太短可能有问题
                    print(f"页面内容过短: {job_url}")
                    return None
                
                # 提取职位数据
                job_data = data_extractor.extract_job_details(result.html, job_url)
                
                if job_data:
                    # 更宽松的数据验证
                    if job_data.get('岗位名称') or job_data.get('薪资情况'):
                        print(f"✓ 成功提取: {job_data.get('岗位名称', 'Unknown')}")
                        return job_data
                
                print(f"✗ 数据提取失败: {job_url}")
                return None
                
            except Exception as e:
                print(f"爬取详情页异常 {job_url}: {str(e)}")
                return None
    
    async def run_enhanced_crawl(self, max_jobs: int = 50) -> Dict[str, str]:
        """运行增强版爬取"""
        print("=" * 60)
        print("BOSS直聘增强版爬虫启动")
        print(f"目标: 获取 {max_jobs} 个职位数据")
        print("=" * 60)
        
        start_time = time.time()
        all_job_urls = []
        
        # 尝试不同的策略获取职位URL
        for i, strategy in enumerate(self.access_strategies, 1):
            print(f"\n执行策略 {i}...")
            try:
                urls = await strategy()
                all_job_urls.extend(urls)
                
                if len(all_job_urls) >= max_jobs:
                    break
                    
            except Exception as e:
                print(f"策略 {i} 执行失败: {str(e)}")
        
        # 去重并限制数量
        unique_urls = list(set(all_job_urls))[:max_jobs]
        print(f"\n总共获取到 {len(unique_urls)} 个唯一职位URL")
        
        if not unique_urls:
            print("没有获取到职位URL")
            return {"json": "", "excel": ""}
        
        # 并发爬取职位详情
        print(f"\n开始并发爬取 {len(unique_urls)} 个职位详情...")
        tasks = [self.enhanced_crawl_job_detail(url) for url in unique_urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤有效结果
        valid_jobs = []
        for result in results:
            if isinstance(result, dict) and result:
                valid_jobs.append(result)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n增强版爬取完成！")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"成功获取: {len(valid_jobs)} 条数据")
        print(f"成功率: {len(valid_jobs)}/{len(unique_urls)} ({len(valid_jobs)/len(unique_urls)*100:.1f}%)")
        
        if valid_jobs:
            # 处理和保存数据
            file_paths = data_processor.process_and_save(valid_jobs)
            return file_paths
        else:
            return {"json": "", "excel": ""}

async def main():
    """主函数"""
    async with EnhancedBOSSCrawler() as crawler:
        result = await crawler.run_enhanced_crawl(max_jobs=20)
        return result

if __name__ == "__main__":
    asyncio.run(main())
