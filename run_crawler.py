#!/usr/bin/env python3
"""
BOSS直聘爬虫启动脚本
简化的用户界面
"""

import asyncio
import sys
from boss_crawler import BOSSCrawler
from config import CITY_CODES

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("    BOSS直聘超高性能全站爬虫 v1.0")
    print("    目标: 5页数据5秒内完成")
    print("    准确提取7个必需字段")
    print("=" * 60)

def get_user_input():
    """获取用户输入"""
    print("\n请选择爬取模式:")
    print("1. 快速测试 (1页数据)")
    print("2. 标准爬取 (5页数据)")
    print("3. 大量爬取 (10页数据)")
    print("4. 自定义设置")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            if choice in ['1', '2', '3', '4']:
                return choice
            else:
                print("无效选择，请输入1-4")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            sys.exit(0)

def get_city_choice():
    """获取城市选择"""
    print("\n请选择城市:")
    print("0. 全国 (推荐)")
    
    cities = list(CITY_CODES.keys())
    for i, city in enumerate(cities, 1):
        print(f"{i}. {city}")
    
    while True:
        try:
            choice = input(f"\n请输入选择 (0-{len(cities)}): ").strip()
            choice_num = int(choice)
            
            if choice_num == 0:
                return None  # 全国
            elif 1 <= choice_num <= len(cities):
                return cities[choice_num - 1]
            else:
                print(f"无效选择，请输入0-{len(cities)}")
        except ValueError:
            print("请输入数字")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            sys.exit(0)

def get_custom_settings():
    """获取自定义设置"""
    city = get_city_choice()
    
    while True:
        try:
            pages = input("\n请输入爬取页数 (1-20): ").strip()
            pages_num = int(pages)
            
            if 1 <= pages_num <= 20:
                return city, pages_num
            else:
                print("页数范围: 1-20")
        except ValueError:
            print("请输入有效数字")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            sys.exit(0)

async def run_crawler_with_settings(city_name, max_pages):
    """运行爬虫"""
    print(f"\n开始爬取...")
    print(f"城市: {'全国' if city_name is None else city_name}")
    print(f"页数: {max_pages}")
    print("-" * 40)
    
    async with BOSSCrawler() as crawler:
        result = await crawler.run_full_crawl(
            city_name=city_name,
            max_pages=max_pages
        )
        
        return result

async def main():
    """主函数"""
    print_banner()
    
    try:
        # 获取用户选择
        choice = get_user_input()
        
        # 根据选择设置参数
        if choice == '1':  # 快速测试
            city_name, max_pages = None, 1
        elif choice == '2':  # 标准爬取
            city_name, max_pages = None, 5
        elif choice == '3':  # 大量爬取
            city_name, max_pages = None, 10
        elif choice == '4':  # 自定义
            city_name, max_pages = get_custom_settings()
        
        # 运行爬虫
        result = await run_crawler_with_settings(city_name, max_pages)
        
        # 显示结果
        if result["json"] or result["excel"]:
            print("\n🎉 爬取任务成功完成!")
            if result["json"]:
                print(f"📄 JSON文件: {result['json']}")
            if result["excel"]:
                print(f"📊 Excel文件: {result['excel']}")
        else:
            print("\n❌ 爬取任务失败")
        
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
    
    print("\n感谢使用BOSS直聘爬虫!")

if __name__ == "__main__":
    asyncio.run(main())
