#!/usr/bin/env python3
"""
增强版BOSS直聘爬虫启动脚本
深度优化反反爬和数据提取准确性
"""

import asyncio
import sys
from enhanced_crawler import EnhancedBOSSCrawler

def print_banner():
    """打印启动横幅"""
    print("=" * 70)
    print("    BOSS直聘增强版超高性能爬虫 v2.0")
    print("    🚀 深度优化反反爬机制")
    print("    🎯 极致提升数据准确性")
    print("    🛡️ 多策略绕过登录限制")
    print("=" * 70)

def get_user_choice():
    """获取用户选择"""
    print("\n请选择爬取模式:")
    print("1. 🎯 智能策略爬取 (推荐)")
    print("2. 🔍 API接口尝试")
    print("3. 📱 移动端接口")
    print("4. 🌐 多策略组合")
    print("5. 🧪 深度测试模式")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-5): ").strip()
            if choice in ['1', '2', '3', '4', '5']:
                return choice
            else:
                print("无效选择，请输入1-5")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            sys.exit(0)

def get_job_count():
    """获取爬取数量"""
    print("\n请选择爬取数量:")
    print("1. 快速测试 (10个职位)")
    print("2. 标准爬取 (20个职位)")
    print("3. 大量爬取 (50个职位)")
    print("4. 自定义数量")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                return 10
            elif choice == '2':
                return 20
            elif choice == '3':
                return 50
            elif choice == '4':
                while True:
                    try:
                        count = int(input("请输入爬取数量 (1-100): "))
                        if 1 <= count <= 100:
                            return count
                        else:
                            print("数量范围: 1-100")
                    except ValueError:
                        print("请输入有效数字")
            else:
                print("无效选择，请输入1-4")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            sys.exit(0)

class EnhancedCrawlerRunner:
    """增强版爬虫运行器"""
    
    def __init__(self):
        self.crawler = None
    
    async def run_smart_strategy(self, max_jobs: int):
        """智能策略爬取"""
        print("\n🎯 启动智能策略爬取...")
        print("📋 策略说明:")
        print("   - 优先使用已知职位URL")
        print("   - 尝试API接口获取")
        print("   - 移动端接口备用")
        print("   - 深度反反爬优化")
        
        async with EnhancedBOSSCrawler() as crawler:
            return await crawler.run_enhanced_crawl(max_jobs=max_jobs)
    
    async def run_api_strategy(self, max_jobs: int):
        """API接口策略"""
        print("\n🔍 启动API接口策略...")
        print("📋 策略说明:")
        print("   - 直接访问搜索API")
        print("   - 绕过前端页面限制")
        print("   - JSON数据直接解析")
        
        async with EnhancedBOSSCrawler() as crawler:
            # 只使用API策略
            crawler.access_strategies = [crawler._strategy_search_api]
            return await crawler.run_enhanced_crawl(max_jobs=max_jobs)
    
    async def run_mobile_strategy(self, max_jobs: int):
        """移动端策略"""
        print("\n📱 启动移动端策略...")
        print("📋 策略说明:")
        print("   - 模拟移动设备访问")
        print("   - 使用移动端接口")
        print("   - 绕过PC端限制")
        
        async with EnhancedBOSSCrawler() as crawler:
            # 只使用移动端策略
            crawler.access_strategies = [crawler._strategy_mobile_interface]
            return await crawler.run_enhanced_crawl(max_jobs=max_jobs)
    
    async def run_multi_strategy(self, max_jobs: int):
        """多策略组合"""
        print("\n🌐 启动多策略组合...")
        print("📋 策略说明:")
        print("   - 并行执行所有策略")
        print("   - 最大化成功率")
        print("   - 数据来源多样化")
        
        async with EnhancedBOSSCrawler() as crawler:
            return await crawler.run_enhanced_crawl(max_jobs=max_jobs)
    
    async def run_deep_test(self, max_jobs: int):
        """深度测试模式"""
        print("\n🧪 启动深度测试模式...")
        print("📋 测试说明:")
        print("   - 详细输出调试信息")
        print("   - 测试各个组件功能")
        print("   - 分析反爬检测结果")
        
        # 先测试单个职位
        print("\n第一步: 测试单个职位爬取...")
        async with EnhancedBOSSCrawler() as crawler:
            test_url = "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html"
            job_data = await crawler.enhanced_crawl_job_detail(test_url)
            
            if job_data:
                print("✅ 单个职位测试成功!")
                print(f"   岗位名称: {job_data.get('岗位名称', 'N/A')}")
                print(f"   薪资情况: {job_data.get('薪资情况', 'N/A')}")
            else:
                print("❌ 单个职位测试失败")
        
        # 然后进行完整爬取
        print("\n第二步: 完整爬取测试...")
        async with EnhancedBOSSCrawler() as crawler:
            return await crawler.run_enhanced_crawl(max_jobs=max_jobs)

async def main():
    """主函数"""
    print_banner()
    
    try:
        # 获取用户选择
        mode_choice = get_user_choice()
        job_count = get_job_count()
        
        print(f"\n开始执行爬取任务...")
        print(f"目标数量: {job_count} 个职位")
        print("-" * 50)
        
        runner = EnhancedCrawlerRunner()
        
        # 根据选择执行不同策略
        if mode_choice == '1':
            result = await runner.run_smart_strategy(job_count)
        elif mode_choice == '2':
            result = await runner.run_api_strategy(job_count)
        elif mode_choice == '3':
            result = await runner.run_mobile_strategy(job_count)
        elif mode_choice == '4':
            result = await runner.run_multi_strategy(job_count)
        elif mode_choice == '5':
            result = await runner.run_deep_test(job_count)
        
        # 显示结果
        print("\n" + "=" * 70)
        if result and (result.get("json") or result.get("excel")):
            print("🎉 爬取任务成功完成!")
            if result.get("json"):
                print(f"📄 JSON文件: {result['json']}")
            if result.get("excel"):
                print(f"📊 Excel文件: {result['excel']}")
            
            print("\n✨ 增强版爬虫功能特点:")
            print("   🛡️ 深度反反爬优化")
            print("   🎯 多策略数据获取")
            print("   📈 极致准确性提升")
            print("   🚀 高性能并发处理")
        else:
            print("❌ 爬取任务失败")
            print("\n💡 可能的原因:")
            print("   - 网站反爬机制升级")
            print("   - 网络连接问题")
            print("   - 需要进一步优化策略")
            
            print("\n🔧 建议解决方案:")
            print("   - 尝试不同的爬取模式")
            print("   - 检查网络连接")
            print("   - 使用深度测试模式分析问题")
        
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
    
    print("\n感谢使用BOSS直聘增强版爬虫!")

if __name__ == "__main__":
    asyncio.run(main())
