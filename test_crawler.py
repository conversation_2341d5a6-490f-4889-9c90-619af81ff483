"""
爬虫测试脚本
快速测试爬虫功能
"""

import asyncio
from boss_crawler import BOSSCrawler

async def test_single_job():
    """测试单个职位爬取"""
    print("测试单个职位爬取...")
    
    async with BOSSCrawler() as crawler:
        # 测试示例URL
        test_url = "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html"
        
        job_data = await crawler.crawl_job_detail(test_url)
        
        if job_data:
            print("✓ 单个职位爬取成功!")
            for key, value in job_data.items():
                print(f"{key}: {value[:100]}..." if len(str(value)) > 100 else f"{key}: {value}")
        else:
            print("✗ 单个职位爬取失败")

async def test_job_list():
    """测试职位列表爬取"""
    print("\n测试职位列表爬取...")
    
    async with BOSSCrawler() as crawler:
        # 测试列表页
        list_url = "https://www.zhipin.com/web/geek/jobs"
        
        job_urls = await crawler.crawl_job_list_page(list_url)
        
        if job_urls:
            print(f"✓ 职位列表爬取成功! 获取到 {len(job_urls)} 个职位URL")
            print("前5个URL:")
            for i, url in enumerate(job_urls[:5]):
                print(f"  {i+1}. {url}")
        else:
            print("✗ 职位列表爬取失败")

async def test_mini_crawl():
    """测试小规模爬取（1页数据）"""
    print("\n测试小规模爬取（1页数据）...")
    
    async with BOSSCrawler() as crawler:
        result = await crawler.run_full_crawl(
            city_name=None,  # 全国
            max_pages=1      # 只爬1页
        )
        
        if result["json"] or result["excel"]:
            print("✓ 小规模爬取测试成功!")
        else:
            print("✗ 小规模爬取测试失败")

async def main():
    """主测试函数"""
    print("=" * 50)
    print("BOSS直聘爬虫功能测试")
    print("=" * 50)
    
    try:
        # 测试1: 单个职位
        await test_single_job()
        
        # 测试2: 职位列表
        await test_job_list()
        
        # 测试3: 小规模爬取
        await test_mini_crawl()
        
        print("\n" + "=" * 50)
        print("所有测试完成!")
        print("=" * 50)
        
    except Exception as e:
        print(f"测试过程中发生异常: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
