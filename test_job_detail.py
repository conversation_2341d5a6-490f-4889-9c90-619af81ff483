"""
测试职位详情页面爬取
"""

import asyncio
from crawl4ai import Async<PERSON>ebCrawler
from anti_detection import anti_detection
from data_extractor import data_extractor

async def test_job_detail():
    """测试职位详情页面"""
    browser_config = anti_detection.get_browser_config()
    crawler = AsyncWebCrawler(config=browser_config)
    
    try:
        await crawler.start()
        
        crawl_config = anti_detection.get_crawl_config()
        # 使用示例URL
        url = "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html"
        
        print(f"正在访问职位详情页: {url}")
        result = await crawler.arun(url=url, **crawl_config)
        
        if result.success:
            print(f"页面获取成功!")
            print(f"页面长度: {len(result.html)}")
            
            # 提取职位数据
            job_data = data_extractor.extract_job_details(result.html, url)
            
            if job_data:
                print("\n✅ 职位数据提取成功!")
                for key, value in job_data.items():
                    print(f"{key}: {value}")
            else:
                print("\n❌ 职位数据提取失败")
                print("页面内容前500字符:")
                print(result.html[:500])
                
        else:
            print(f"页面获取失败: {result.error_message}")
            
    except Exception as e:
        print(f"异常: {str(e)}")
    finally:
        await crawler.close()

if __name__ == "__main__":
    asyncio.run(test_job_detail())
