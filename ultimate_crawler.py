"""
终极版BOSS直聘爬虫
使用最强反反爬策略绕过所有限制
"""

import asyncio
import time
import random
from typing import List, Dict, Any
from crawl4ai import AsyncWebCrawler, BrowserConfig
from urllib.parse import urljoin

from config import BASE_URL, MAX_CONCURRENT_REQUESTS
from data_extractor import data_extractor
from data_processor import data_processor

class UltimateBOSSCrawler:
    """终极版BOSS直聘爬虫"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.crawler = None
        self.semaphore = asyncio.Semaphore(5)  # 降低并发数
        
        # 已知有效的职位URL（可以通过其他渠道获取）
        self.known_job_urls = [
            "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html",
            # 可以添加更多已知URL
        ]
    
    async def __aenter__(self):
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    def get_ultimate_browser_config(self) -> BrowserConfig:
        """获取终极浏览器配置"""
        # 使用最真实的浏览器配置
        config = BrowserConfig(
            headless=False,  # 使用有头模式，更难被检测
            browser_type="chromium",
            viewport_width=1366,
            viewport_height=768,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_args=[
                # 最小化反检测参数
                "--no-sandbox",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--lang=zh-CN",
                "--accept-lang=zh-CN,zh;q=0.9,en;q=0.8",
                # 移除明显的自动化标识
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-automation",
            ],
            headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }
        )
        
        return config
    
    def get_ultimate_crawl_config(self) -> Dict[str, Any]:
        """获取终极爬取配置"""
        return {
            "word_count_threshold": 10,
            "css_selector": None,
            "screenshot": False,
            "wait_for": "networkidle",
            "timeout": 60000,  # 1分钟超时
            "page_timeout": 120000,  # 2分钟页面超时
            "magic": True,
            "simulate_user": True,
            "override_navigator": True,
            "delay_before_return_html": 20.0,  # 等待20秒
            "js_code": [
                """
                // 终极反检测JS代码
                
                // 移除所有自动化标识
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // 模拟真实浏览器环境
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer'},
                        {name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai'},
                        {name: 'Native Client', filename: 'internal-nacl-plugin'}
                    ],
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en-US', 'en'],
                });
                
                Object.defineProperty(navigator, 'platform', {
                    get: () => 'Win32',
                });
                
                // 模拟真实用户行为
                const simulateUserBehavior = async () => {
                    // 随机移动鼠标
                    const event = new MouseEvent('mousemove', {
                        clientX: Math.random() * window.innerWidth,
                        clientY: Math.random() * window.innerHeight
                    });
                    document.dispatchEvent(event);
                    
                    // 随机滚动
                    window.scrollTo(0, Math.random() * 200);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    window.scrollTo(0, 0);
                    
                    // 等待页面稳定
                    await new Promise(resolve => setTimeout(resolve, 3000));
                };
                
                // 等待页面完全加载
                if (document.readyState !== 'complete') {
                    await new Promise(resolve => {
                        window.addEventListener('load', resolve);
                    });
                }
                
                // 执行用户行为模拟
                await simulateUserBehavior();
                
                // 检查并等待安全检查页面
                let waitCount = 0;
                while ((document.title === '请稍候' || document.body.innerText.includes('请稍候')) && waitCount < 15) {
                    console.log('检测到安全检查页面，等待中...', waitCount);
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    waitCount++;
                    
                    // 再次模拟用户行为
                    if (waitCount % 3 === 0) {
                        await simulateUserBehavior();
                    }
                }
                
                // 最终等待
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                console.log('页面加载完成，标题:', document.title);
                """
            ]
        }
    
    async def start(self):
        """启动爬虫"""
        browser_config = self.get_ultimate_browser_config()
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.start()
        print("终极版爬虫已启动")
    
    async def close(self):
        """关闭爬虫"""
        if self.crawler:
            await self.crawler.close()
        print("终极版爬虫已关闭")
    
    async def ultimate_crawl_job_detail(self, job_url: str) -> Dict[str, str]:
        """终极版职位详情爬取"""
        async with self.semaphore:
            try:
                print(f"🎯 正在爬取: {job_url}")
                
                # 随机延时
                await asyncio.sleep(random.uniform(3.0, 8.0))
                
                crawl_config = self.get_ultimate_crawl_config()
                result = await self.crawler.arun(url=job_url, **crawl_config)
                
                if not result.success:
                    print(f"❌ 页面获取失败: {job_url}")
                    return None
                
                print(f"📄 页面长度: {len(result.html)}")
                print(f"📋 页面标题: {result.html[:100]}...")
                
                # 检查是否还是安全检查页面
                if "请稍候" in result.html or len(result.html) < 5000:
                    print(f"⚠️ 仍然是安全检查页面或内容过短: {job_url}")
                    return None
                
                # 提取职位数据
                job_data = data_extractor.extract_job_details(result.html, job_url)
                
                if job_data:
                    # 更宽松的验证
                    if job_data.get('岗位名称') or job_data.get('薪资情况') or len([v for v in job_data.values() if v]) >= 3:
                        print(f"✅ 成功提取: {job_data.get('岗位名称', 'Unknown')}")
                        return job_data
                
                print(f"❌ 数据提取失败: {job_url}")
                return None
                
            except Exception as e:
                print(f"❌ 爬取异常 {job_url}: {str(e)}")
                return None
    
    async def run_ultimate_crawl(self, max_jobs: int = 10) -> Dict[str, str]:
        """运行终极版爬取"""
        print("=" * 70)
        print("🚀 BOSS直聘终极版爬虫启动")
        print("💪 使用最强反反爬策略")
        print(f"🎯 目标: 获取 {max_jobs} 个职位数据")
        print("=" * 70)
        
        start_time = time.time()
        
        # 使用已知URL + 生成的URL
        all_urls = self.known_job_urls.copy()
        
        # 生成更多可能的URL（基于已知模式）
        for i in range(max_jobs - len(all_urls)):
            # 简单的URL生成策略
            random_id = f"{''.join(random.choices('abcdef0123456789', k=16))}1X{''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=8))}"
            url = f"https://www.zhipin.com/job_detail/{random_id}.html"
            all_urls.append(url)
        
        # 限制URL数量
        target_urls = all_urls[:max_jobs]
        print(f"📋 准备爬取 {len(target_urls)} 个职位URL")
        
        # 串行爬取（避免并发触发更强的反爬）
        valid_jobs = []
        for i, url in enumerate(target_urls, 1):
            print(f"\n进度: {i}/{len(target_urls)}")
            job_data = await self.ultimate_crawl_job_detail(url)
            
            if job_data:
                valid_jobs.append(job_data)
                print(f"✅ 当前成功: {len(valid_jobs)} 个")
            
            # 如果已经获取足够数据，可以提前结束
            if len(valid_jobs) >= max_jobs // 2:  # 至少获取一半数据
                break
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n" + "=" * 70)
        print("🎉 终极版爬取完成！")
        print(f"⏱️ 总耗时: {total_time:.2f} 秒")
        print(f"📊 成功获取: {len(valid_jobs)} 条数据")
        print(f"📈 成功率: {len(valid_jobs)}/{len(target_urls)} ({len(valid_jobs)/len(target_urls)*100:.1f}%)")
        
        if valid_jobs:
            # 处理和保存数据
            file_paths = data_processor.process_and_save(valid_jobs)
            print(f"📄 JSON文件: {file_paths['json']}")
            print(f"📊 Excel文件: {file_paths['excel']}")
            return file_paths
        else:
            print("❌ 没有获取到有效数据")
            return {"json": "", "excel": ""}

async def main():
    """主函数"""
    async with UltimateBOSSCrawler() as crawler:
        result = await crawler.run_ultimate_crawl(max_jobs=5)
        return result

if __name__ == "__main__":
    asyncio.run(main())
