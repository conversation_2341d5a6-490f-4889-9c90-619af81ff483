# BOSS直聘爬虫 - 快速启动指南

## 🚀 一键启动

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动爬虫（推荐）
python run_crawler.py
```

## 📋 选择模式

启动后选择爬取模式：
- **1** - 快速测试 (1页数据，约10-20个职位)
- **2** - 标准爬取 (5页数据，约50-100个职位) ⭐推荐
- **3** - 大量爬取 (10页数据，约100-200个职位)
- **4** - 自定义设置 (选择城市+页数)

## 🎯 性能目标

- ⚡ **5页数据5秒内完成**
- 🎯 **100%准确提取7个字段**
- 🛡️ **反反爬机制保证成功率**
- 📊 **同时输出JSON和Excel**

## 📁 输出文件

程序会在 `output/` 目录生成：
- `boss_jobs_YYYYMMDD_HHMMSS.json`
- `boss_jobs_YYYYMMDD_HHMMSS.xlsx`

## 🔧 其他启动方式

```bash
# 直接运行主程序
python boss_crawler.py

# 功能测试
python test_crawler.py
```

## ⚠️ 注意事项

1. 首次运行可能需要下载浏览器驱动
2. 请确保网络连接稳定
3. 遵守网站使用条款，合理控制爬取频率

## 🆘 常见问题

**Q: 爬取失败怎么办？**
A: 程序内置重试机制，如持续失败可能是网络问题或反爬限制

**Q: 如何提高成功率？**
A: 降低并发数，增加延时间隔（修改config.py）

**Q: 数据不完整？**
A: 程序会自动过滤不完整数据，确保输出质量
